#Requires AutoHotkey v2
#include UIA-v2-1.1.0\Lib\UIA.ahk

; 启动 CoppeliaSim
Run("coppliasim.exe")
WinWaitActive("ahk_exe coppliasim.exe")

hwnd := WinExist("ahk_exe coppliasim.exe")
uia := UIA()
main := uia.ElementFromHandle(hwnd)

; 示例：查找并点击“Start Simulation”按钮
btn := main.FindFirstByName("Start Simulation")
if btn {
    btn.Click()
    MsgBox("已点击 Start Simulation 按钮")
} else {
    MsgBox("未找到 Start Simulation 按钮")
}