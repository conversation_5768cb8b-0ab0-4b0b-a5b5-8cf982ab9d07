#Requires AutoHotkey v2
#include UIA-v2-1.1.0\Lib\UIA.ahk

; 获取当前活动窗口进行UIA测试
MsgBox("请切换到您想要测试的窗口，然后点击确定")

; 获取当前活动窗口
hwnd := WinExist("A")
if !hwnd {
    MsgBox("无法获取活动窗口")
    ExitApp
}

; 获取窗口标题
winTitle := WinGetTitle(hwnd)
MsgBox("正在测试窗口: " . winTitle)

try {
    ; 获取UIA元素
    mainElement := UIA.ElementFromHandle(hwnd)
    
    ; 显示窗口基本信息
    elementName := mainElement.Name ? mainElement.Name : "无名称"
    elementType := mainElement.Type ? mainElement.Type : "未知类型"
    
    result := "窗口UIA信息:`n"
    result .= "名称: " . elementName . "`n"
    result .= "类型: " . elementType . "`n`n"
    
    ; 查找所有编辑控件
    editControls := mainElement.FindAllByType("Edit")
    if editControls.Length > 0 {
        result .= "找到 " . editControls.Length . " 个编辑控件:`n"
        for i, editCtrl in editControls {
            editName := editCtrl.Name ? editCtrl.Name : "编辑控件" . i
            editValue := editCtrl.Value ? editCtrl.Value : "(空)"
            result .= "- " . editName . ": " . editValue . "`n"
        }
    } else {
        result .= "未找到编辑控件`n"
    }
    
    ; 查找所有按钮
    buttons := mainElement.FindAllByType("Button")
    if buttons.Length > 0 {
        result .= "`n找到 " . buttons.Length . " 个按钮:`n"
        for i, btn in buttons {
            btnName := btn.Name ? btn.Name : "按钮" . i
            result .= "- " . btnName . "`n"
        }
    }
    
    MsgBox(result)
    
} catch Error as e {
    MsgBox("UIA测试失败: " . e.Message)
}
